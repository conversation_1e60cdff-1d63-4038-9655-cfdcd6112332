# aegis_pipeline.py
# This script creates an NVIDIA Morpheus pipeline to act as our Aegis Shield.
# It reads from Kafka, applies triage logic, and routes messages.

import morpheus
from morpheus.config import Config
from morpheus.pipeline import LinearPipeline
from morpheus.stages.input.kafka_source_stage import KafkaSourceStage
from morpheus.stages.preprocess.deserialize_stage import DeserializeStage
from morpheus.stages.output.kafka_output_stage import KafkaOutputStage
from morpheus.stages.general.linear_stage import LinearStage
from morpheus.messages import MessageMeta
import logging

# --- Configuration ---
# CRITICAL CHANGE: When this script runs inside a Docker container, it connects to Kafka
# using the service name 'kafka' and the internal port '29092' defined in docker-compose.yml.
KAFKA_BOOTSTRAP_SERVERS = 'kafka:29092'
KAFKA_INPUT_TOPIC = 'social_media_raw'
KAFKA_LOGS_TOPIC = 'aegis_logs'
KAFKA_ESCALATION_TOPIC = 'kairos_escalations'

# Configure Morpheus logging to see what's happening
logging.basicConfig(level=logging.INFO)

# --- Triage Logic ---
def triage_claim(message: MessageMeta):
    """
    This is our simple, placeholder triage model. It inspects the message
    and adds a 'triage_decision' field to the message metadata.
    In a production system, this stage would be a TritonInferenceStage
    calling multiple, powerful AI models (NIMs).
    """
    # Use try-except block for safety in case a message is malformed
    try:
        post_text = message.get_meta("post").lower()
    except (AttributeError, KeyError):
        # If 'post' key is missing, treat as benign and move on
        message.set_meta("triage_decision", "BENIGN")
        message.set_meta("details", "Malformed message, no 'post' field.")
        return message

    # Simple keyword-based rules for our prototype
    if "chemtrails" in post_text or "free crypto" in post_text or "faked" in post_text:
        message.set_meta("triage_decision", "MISINFO_CRITICAL")
        message.set_meta("details", "High confidence keyword match for known misinformation.")
    elif ("ai" in post_text and "job" in post_text) or "policy" in post_text or "vaccines" in post_text:
        message.set_meta("triage_decision", "ESCALATE_TO_KAIROS")
        message.set_meta("details", "Complex, belief-driven, emotional claim identified.")
    else:
        message.set_meta("triage_decision", "BENIGN")
        message.set_meta("details", "No threat detected.")
        
    return message

def main():
    """
    Defines and runs the Morpheus pipeline.
    """
    config = Config()
    
    # Configure Morpheus for real-time, low-latency processing.
    config.pipeline_batch_size = 1
    config.model_max_batch_size = 1
    config.edge_buffer_size = 1

    # Create a pipeline object
    pipe = LinearPipeline(config)
    
    # 1. SOURCE: Read from the raw social media Kafka topic.
    pipe.set_source(KafkaSourceStage(config, 
                                     bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
                                     input_topic=KAFKA_INPUT_TOPIC,
                                     group_id="aegis-group", # Add a group id
                                     auto_offset_reset="earliest"))
    
    # 2. PREPROCESSING: Convert the raw JSON strings from Kafka into structured Morpheus messages.
    pipe.add_stage(DeserializeStage(config))
    
    # 3. TRIAGE: Apply our custom triage logic using a simple linear stage.
    pipe.add_stage(LinearStage(config, triage_claim))

    # This is a simple stage to print every message that passes through, for debugging.
    def print_message(message: MessageMeta):
        print(f"Aegis Triage: Decision='{message.get_meta('triage_decision')}' for post='{message.get_meta('post')[:50]}...'")
        return message
    pipe.add_stage(LinearStage(config, print_message))
    
    # 4. OUTPUT ROUTER: Split the pipeline to send messages to different destinations.
    # The .tee() method creates a copy of the stream.
    tee = pipe.tee()

    # Define a filter for messages that should be escalated to Kairos
    escalation_filter = morpheus.stages.general.filter_stage.FilterStage(
        config, filter_lambda=lambda x: x.get_meta("triage_decision") == "ESCALATE_TO_KAIROS"
    )

    # Define a filter for all other messages (logs)
    log_filter = morpheus.stages.general.filter_stage.FilterStage(
        config, filter_lambda=lambda x: x.get_meta("triage_decision") != "ESCALATE_TO_KAIROS"
    )

    # Add the filters to the two branches of our "tee"
    pipe_escalations = tee.add_stage(escalation_filter)
    pipe_logs = tee.add_stage(log_filter)

    # Attach the Kafka output stages to the end of each filtered branch
    pipe_escalations.add_stage(KafkaOutputStage(config, 
                                                topic=KAFKA_ESCALATION_TOPIC, 
                                                bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS))
    
    pipe_logs.add_stage(KafkaOutputStage(config, 
                                         topic=KAFKA_LOGS_TOPIC, 
                                         bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS))
    
    print("Aegis Shield Initialized. Starting Morpheus pipeline inside Docker...")
    # This will run indefinitely, processing messages as they arrive from the streamer.
    pipe.run()


if __name__ == "__main__":
    main()