# Use the official Morpheus container as our base
FROM nvcr.io/nvidia/morpheus/morpheus:24.03-runtime

# Set the working directory inside the container
WORKDIR /workspace

# Copy our Aegis pipeline script into the container
COPY aegis_pipeline.py .

# Install the kafka-python client library inside the container
RUN pip install kafka-python

# The command to run when the container starts
CMD ["python", "aegis_pipeline.py"]