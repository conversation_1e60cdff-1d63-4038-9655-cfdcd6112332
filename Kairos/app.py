# app.py
# This script creates the Gradio-based "Mission Control" UI for the Kairos-Aegis Protocol.

import gradio as gr
import requests # Used for synchronous HTTP requests to our backend
import time

# --- Configuration ---
# This should be the address of the FastAPI backend you would create.
# For this prototype, we'll assume it's running locally on port 8001.
BACKEND_API_URL = "http://localhost:8001"
KAIROS_CHAT_ENDPOINT = f"{BACKEND_API_URL}/chat/kairos"

# --- <PERSON><PERSON> Chat Logic ---
# This function defines how the chatbot interacts with our backend API.
def kairos_chat_turn(user_input, history):
    """
    Handles a single turn of the conversation with the Kairos agent.
    
    Args:
        user_input (str): The text typed by the human operator.
        history (list): The Gradio-managed conversation history.
    """
    # Append the user's message to the history for the API call
    # The history format for the API is a list of dictionaries: [{"role": "user", "content": "..."}]
    api_history = []
    for user_msg, ai_msg in history:
        api_history.append({"role": "user", "content": user_msg})
        api_history.append({"role": "assistant", "content": ai_msg})
    api_history.append({"role": "user", "content": user_input})
    
    # Call the FastAPI backend to get the LLM's response
    try:
        response = requests.post(KAIROS_CHAT_ENDPOINT, json={"history": api_history})
        response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)
        ai_response = response.json()
        
        # The API returns a dictionary like {"role": "assistant", "content": "..."}
        # We append this new message to our Gradio history.
        history.append((user_input, ai_response.get("content", "Error: No content in response.")))
        
    except requests.exceptions.RequestException as e:
        # Handle connection errors or bad responses gracefully
        error_message = f"Error connecting to backend: {e}"
        history.append((user_input, error_message))

    # Gradio expects the chat history and an empty string to clear the input box
    return history, ""


# --- UI Definition ---
def create_mission_control_ui():
    """
    Builds the complete Gradio interface.
    """
    # Using gr.Blocks() for more control over the layout.
    with gr.Blocks(theme=gr.themes.Soft(), title="Kairos-Aegis Mission Control") as demo:
        gr.Markdown("# 🛡️ Kairos-Aegis Mission Control")

        with gr.Tabs():
            with gr.TabItem("🚨 Aegis Live Feed"):
                gr.Markdown("## Real-time Triage Stream from Aegis Shield")
                gr.Markdown("_(This is a placeholder UI. A real implementation would use WebSockets to stream live data from the Morpheus pipeline into these boxes.)_")
                
                with gr.Row():
                    # In a real app, these would be populated by a JavaScript WebSocket client.
                    benign_box = gr.JSON(label="✅ Benign Traffic", value={"info": "No threats detected."})
                    misinfo_box = gr.JSON(label="🚫 Critical Misinformation", value={"user": "bot774", "post": "Free crypto!", "details": "High confidence keyword match."})
                    escalation_box = gr.JSON(label="⚠️ Escalation to Kairos", value={"user": "anxious_worker", "post": "AI will take my job...", "details": "Complex, belief-driven claim."})

            with gr.TabItem("🧠 Kairos Intervention Dialogue"):
                gr.Markdown("## Engage in a Socratic Dialogue with Kairos")
                gr.Markdown("An escalation has been received. You can now act as the operator and guide the conversation with the Kairos agent.")
                
                # The ChatInterface is a high-level component that simplifies creating a chatbot.
                gr.ChatInterface(
                    fn=kairos_chat_turn,
                    chatbot=gr.Chatbot(height=500, label="Kairos Dialogue"),
                    textbox=gr.Textbox(placeholder="Type your guidance or question here...", scale=7),
                    title="Kairos Intervention Agent",
                    description="Guide the AI to explore the user's belief system.",
                    examples=[
                        ["Let's begin the Mirror stage."],
                        ["Now, simulate the consequences of that belief."],
                        ["Ask a question about personal responsibility."]
                    ]
                )

    return demo


# --- Main Execution ---
if __name__ == "__main__":
    mission_control = create_mission_control_ui()
    
    # Launch the Gradio web server. It will provide a local URL to open in your browser.
    mission_control.launch()