# docker-compose.yml
# Updated to use a specific, versioned tag for the Llama 3.1 NIM.

version: '3.8'

services:
  # Service 1: Zookeeper for Kafka Cluster Management
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.3
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  # Service 2: Kafka Message Broker
  kafka:
    image: confluentinc/cp-kafka:7.5.3
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAF<PERSON>_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0

  # Service 3: The Aegis Triage Pipeline (Morpheus)
  aegis-pipeline:
    build:
      context: .
      dockerfile: Dockerfile.aegis
    container_name: aegis-pipeline
    depends_on:
      - kafka
    platform: linux/amd64
    environment:
      - NGC_API_KEY=${NGC_API_KEY}

  # Service 4: The Kairos "Brain" (NVIDIA Inference Microservice)
  nim-llama3:
    # --- THIS LINE HAS BEEN UPDATED ---
    image: nvcr.io/nim/meta/llama-3.1-8b-instruct-pb24h2:1.3.8
    container_name: nim-llama3
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    ports:
      - "8000:8000"
    environment:
      - NGC_API_KEY=${NGC_API_KEY}