import kafka
import json
import time
import random

producer = kafka.KafkaProducer(bootstrap_servers='localhost:9092',
                               value_serializer=lambda v: json.dumps(v).encode('utf-8'))

# Expanded set of posts
social_media_stream = [
    {"user": "user123", "post": "My cat is napping. #cats"},
    {"user": "truther99", "post": "They are putting mind control chemicals in the water!"},
    # ... add 20-30 more varied examples
]

print("Starting data streamer...")
while True:
    message = random.choice(social_media_stream)
    message['timestamp'] = time.time()
    print(f"Streaming: {message['post']}")
    producer.send('social_media_raw', value=message)
    time.sleep(random.uniform(1, 3)) # Stream a post every 1-3 seconds