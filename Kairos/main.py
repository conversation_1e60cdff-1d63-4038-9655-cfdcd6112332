from fastapi import FastAP<PERSON>, WebSocket
from fastapi.websockets import WebSocketDisconnect
import kafka
import json
import asyncio
import httpx # For async requests to the NIM

app = FastAPI()
KAIROS_NIM_URL = "http://localhost:8000/v1/chat/completions" # Assuming NIM is running

# --- WebSocket for Live Aegis Feed ---
@app.websocket("/ws/aegis-feed")
async def aegis_feed(websocket: WebSocket):
    await websocket.accept()
    consumer = kafka.KafkaConsumer(
        'aegis_triage_results',
        bootstrap_servers='localhost:9092',
        value_deserializer=lambda v: json.loads(v.decode('utf-8')))
    
    try:
        for message in consumer:
            await websocket.send_json(message.value)
    except WebSocketDisconnect:
        print("Aegis feed client disconnected.")

# --- HTTP Endpoint for Kairos Chat ---
@app.post("/chat/kairos")
async def kairos_chat_handler(payload: dict):
    # payload = {"history": [{"role": "system", ...}, {"role": "user", ...}]}
    async with httpx.AsyncClient(timeout=60.0) as client:
        response = await client.post(
            KAIROS_NIM_URL,
            json={"model": "meta/llama3-8b-instruct", "messages": payload["history"]}
        )
        return response.json()['choices'][0]['message']